import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";

import { Interview } from "@/types";

import { CustomBreadCrumb } from "./custom-bread-crumb";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@clerk/clerk-react";
import { toast } from "sonner";
import { Headings } from "./headings";
import { Button } from "./ui/button";
import { Loader, Trash2 } from "lucide-react";
import { Separator } from "./ui/separator";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "./ui/form";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { chatSession } from "@/scripts";
import { addDoc, collection, doc, serverTimestamp, updateDoc } from "firebase/firestore";
import { db } from "@/config/firebase.config";

interface FormMockInterviewProps {
  initialData: Interview | null;
}

const formSchema = z.object({
  position: z.string().min(1, "Position is required").max(100, "Position must be 100 characters or less"),
  description: z.string().min(10, "Description is required"),
  experience: z.coerce.number().min(0, "Experience cannot be empty or negative"),
  techStack: z.string().min(1, "Tech stack must be at least a character"),
});

type FormData = z.infer<typeof formSchema>;

export const FormMockInterview = ({ initialData }: FormMockInterviewProps) => {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {},
  });

  const { isValid, isSubmitting } = form.formState;
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { userId } = useAuth();

  const title = initialData ? initialData.position : "Create a new mock interview";

  const breadCrumpPage = initialData ? initialData?.position : "Create";
  const actions = initialData ? "Save Changes" : "Create";
  const toastMessage = initialData
    ? { title: "Updated..!", description: "Changes saved successfully..." }
    : { title: "Created..!", description: "New Mock Interview created..." };

  const cleanAiResponse = (responseText: string) => {
    // Step 1: Trim any surrounding whitespace
    let cleanText = responseText.trim();

    // Step 2: Remove any occurrences of "json" or code block symbols (``` or `)
    cleanText = cleanText.replace(/(json|```|`)/g, "");

    // Step 3: Remove control characters that can break JSON parsing
    // This removes characters like \b, \f, \r, \v, and other control chars except \n and \t
    cleanText = cleanText.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, "");

    // Step 4: Fix common JSON formatting issues
    // Replace smart quotes with regular quotes
    cleanText = cleanText.replace(/[""]/g, '"');
    cleanText = cleanText.replace(/['']/g, "'");

    // Step 5: Extract a JSON array by capturing text between square brackets
    const jsonArrayMatch = cleanText.match(/\[.*\]/s);
    if (jsonArrayMatch) {
      cleanText = jsonArrayMatch[0];
    } else {
      throw new Error("No JSON array found in response");
    }

    // Step 6: Parse the clean JSON text into an array of objects
    try {
      return JSON.parse(cleanText);
    } catch (error) {
      console.error("JSON parsing failed. Raw response:", responseText);
      console.error("Cleaned response:", cleanText);
      throw new Error("Invalid JSON format: " + (error as Error)?.message);
    }
  };

  const generateAiResponseWithRetry = async (data: FormData, maxRetries = 2) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await generateAiResponse(data);
      } catch (error) {
        console.log(`AI generation attempt ${attempt} failed:`, error);

        if (attempt === maxRetries) {
          throw error; // Final attempt failed, throw the error
        }

        // Wait before retrying (exponential backoff)
        await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
      }
    }
  };

  const generateAiResponse = async (data: FormData) => {
    const prompt = `
        As an experienced prompt engineer, generate a JSON array containing 5 technical interview questions along with detailed answers based on the following job information. Each object in the array should have the fields "question" and "answer", formatted as follows:

        [
          { "question": "<Question text>", "answer": "<Answer text>" },
          ...
        ]

        Job Information:
        - Job Position: ${data?.position}
        - Job Description: ${data?.description}
        - Years of Experience Required: ${data?.experience}
        - Tech Stacks: ${data?.techStack}

        The questions should assess skills in ${data?.techStack} development and best practices, problem-solving, and experience handling complex requirements. Please format the output strictly as an array of JSON objects without any additional labels, code blocks, or explanations. Return only the JSON array with questions and answers.
        `;

    try {
      const aiResult = await chatSession.sendMessage(prompt);
      const cleanedResponse = cleanAiResponse(aiResult.response.text());
      return cleanedResponse;
    } catch (error) {
      console.error("AI generation error:", error);

      // Handle specific AI API errors
      if (error instanceof Error) {
        if (error.message.includes("network") || error.message.includes("fetch")) {
          throw new Error("Network error: Unable to connect to AI service. Please check your internet connection.");
        }
        if (error.message.includes("quota") || error.message.includes("limit")) {
          throw new Error("API quota exceeded. Please try again later.");
        }
        if (error.message.includes("Invalid JSON format")) {
          throw new Error("AI response formatting error. Please try again.");
        }
      }

      // Re-throw the original error if it's not a known type
      throw error;
    }
  };

  const checkNetworkConnectivity = () => {
    return navigator.onLine;
  };

  const handleFirebaseError = (error: any) => {
    console.error("Firebase error:", error);

    if (error?.code === "unavailable" || error?.message?.includes("offline")) {
      toast.error("Connection Error", {
        description: "You appear to be offline. Please check your internet connection and try again.",
      });
      return;
    }

    if (error?.code === "permission-denied") {
      toast.error("Permission Error", {
        description: "You don't have permission to perform this action. Please sign in again.",
      });
      return;
    }

    if (error?.code === "unauthenticated") {
      toast.error("Authentication Error", {
        description: "Your session has expired. Please sign in again.",
      });
      return;
    }

    // Generic error fallback
    toast.error("Error", {
      description: "Something went wrong. Please try again later.",
    });
  };

  const onSubmit = async (data: FormData) => {
    try {
      setLoading(true);

      // Check network connectivity first
      if (!checkNetworkConnectivity()) {
        toast.error("No Internet Connection", {
          description: "Please check your internet connection and try again.",
        });
        return;
      }

      if (initialData) {
        // update
        if (isValid) {
          try {
            const aiResult = await generateAiResponseWithRetry(data);

            await updateDoc(doc(db, "interviews", initialData?.id), {
              questions: aiResult,
              ...data,
              updatedAt: serverTimestamp(),
            });
            toast(toastMessage.title, { description: toastMessage.description });
          } catch (aiError) {
            if (aiError instanceof Error && aiError.message.includes("Network error")) {
              toast.error("AI Service Error", {
                description: aiError.message,
              });
              return;
            }
            throw aiError; // Re-throw to be handled by outer catch
          }
        }
      } else {
        // create a new mock interview
        if (isValid) {
          try {
            const aiResult = await generateAiResponseWithRetry(data);

            await addDoc(collection(db, "interviews"), {
              ...data,
              userId,
              questions: aiResult,
              createdAt: serverTimestamp(),
            });

            toast(toastMessage.title, { description: toastMessage.description });
          } catch (aiError) {
            if (aiError instanceof Error && aiError.message.includes("Network error")) {
              toast.error("AI Service Error", {
                description: aiError.message,
              });
              return;
            }
            throw aiError; // Re-throw to be handled by outer catch
          }
        }
      }

      navigate("/generate", { replace: true });
    } catch (error) {
      handleFirebaseError(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (initialData) {
      form.reset({
        position: initialData.position,
        description: initialData.description,
        experience: initialData.experience,
        techStack: initialData.techStack,
      });
    }
  }, [initialData, form]);

  return (
    <div className="w-full flex-col space-y-4">
      <CustomBreadCrumb
        breadCrumbPage={breadCrumpPage}
        breadCrumpItems={[{ label: "Mock Interviews", link: "/generate" }]}
      />

      <div className="mt-4 flex items-center justify-between w-full">
        <Headings title={title} isSubHeading />

        {initialData && (
          <Button size={"icon"} variant={"ghost"}>
            <Trash2 className="min-w-4 min-h-4 text-red-500" />
          </Button>
        )}
      </div>

      <Separator className="my-4" />

      <div className="my-6"></div>

      <FormProvider {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full p-8 rounded-lg flex-col flex items-start justify-start gap-6 shadow-md "
        >
          <FormField
            control={form.control}
            name="position"
            render={({ field }) => (
              <FormItem className="w-full space-y-4">
                <div className="w-full flex items-center justify-between">
                  <FormLabel>Job Role / Job Position</FormLabel>
                  <FormMessage className="text-sm" />
                </div>
                <FormControl>
                  <Input
                    className="h-12"
                    disabled={loading}
                    placeholder="eg:- Full Stack Developer"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem className="w-full space-y-4">
                <div className="w-full flex items-center justify-between">
                  <FormLabel>Job Description</FormLabel>
                  <FormMessage className="text-sm" />
                </div>
                <FormControl>
                  <Textarea
                    className="h-12"
                    disabled={loading}
                    placeholder="eg:- describle your job role"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="experience"
            render={({ field }) => (
              <FormItem className="w-full space-y-4">
                <div className="w-full flex items-center justify-between">
                  <FormLabel>Years of Experience</FormLabel>
                  <FormMessage className="text-sm" />
                </div>
                <FormControl>
                  <Input
                    type="number"
                    className="h-12"
                    disabled={loading}
                    placeholder="eg:- 5 Years"
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="techStack"
            render={({ field }) => (
              <FormItem className="w-full space-y-4">
                <div className="w-full flex items-center justify-between">
                  <FormLabel>Tech Stacks</FormLabel>
                  <FormMessage className="text-sm" />
                </div>
                <FormControl>
                  <Textarea
                    className="h-12"
                    disabled={loading}
                    placeholder="eg:- React, Typescript..."
                    {...field}
                    value={field.value || ""}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <div className="w-full flex items-center justify-end gap-6">
            <Button type="reset" size={"sm"} variant={"outline"} disabled={isSubmitting || loading}>
              Reset
            </Button>
            <Button type="submit" size={"sm"} disabled={isSubmitting || !isValid || loading}>
              {loading ? <Loader className="text-gray-50 animate-spin" /> : actions}
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};
