import { MainRoutes } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import { NavLink } from "react-router-dom";

interface NavigationRoutesProps {
  isMobile?: boolean;
}

export const NavigationRoutes = ({ isMobile = false }: NavigationRoutesProps) => {
  return (
    <ul className={cn("flex items-center gap-2", isMobile && "items-start flex-col gap-6")}>
      {MainRoutes.map((route) => (
        <li key={route.href}>
          <NavLink
            to={route.href}
            className={({ isActive }) =>
              cn(
                "text-base text-neutral-600 hover:text-neutral-900 transition-colors px-3 py-2 rounded-md hover:bg-muted/50",
                isActive && "text-neutral-900 font-semibold bg-muted",
                isMobile && "block w-full"
              )
            }
          >
            {route.label}
          </NavLink>
        </li>
      ))}
    </ul>
  );
};
