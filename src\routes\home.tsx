import { <PERSON><PERSON><PERSON>, <PERSON>, Target, Users, Award, Clock, CheckCircle, ArrowRight } from "lucide-react";
import <PERSON>quee from "react-fast-marquee";

import { Container } from "@/components/container";
import { HeroSection } from "@/components/hero-section";
import { FeatureSection } from "@/components/feature-section";
import { StatsSection } from "@/components/stats-section";
import { TestimonialCard } from "@/components/testimonial-card";
import { ServiceCard } from "@/components/service-card";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MarqueImg } from "@/components/marquee-img";
import { Link } from "react-router-dom";

const HomePage = () => {
  const features = [
    {
      icon: Brain,
      title: "AI-Powered Interviews",
      description: "Practice with our advanced AI that adapts to your responses and provides real-time feedback to improve your performance."
    },
    {
      icon: Target,
      title: "Industry-Specific Questions",
      description: "Get questions tailored to your specific role and industry, ensuring relevant and targeted preparation."
    },
    {
      icon: Users,
      title: "Expert Guidance",
      description: "Learn from industry professionals and get insights from successful candidates who've landed their dream jobs."
    },
    {
      icon: Award,
      title: "Proven Results",
      description: "Join thousands of successful candidates who have improved their interview skills and landed top positions."
    },
    {
      icon: Clock,
      title: "24/7 Availability",
      description: "Practice anytime, anywhere with our always-available AI interviewer and comprehensive resources."
    },
    {
      icon: CheckCircle,
      title: "Progress Tracking",
      description: "Monitor your improvement with detailed analytics and performance metrics over time."
    }
  ];

  const stats = [
    { value: "50K+", label: "Successful Interviews", description: "Candidates placed in top companies" },
    { value: "95%", label: "Success Rate", description: "Of users get job offers" },
    { value: "500+", label: "Companies", description: "Trust our training methods" },
    { value: "4.9", label: "Rating", description: "Average user satisfaction" }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Software Engineer",
      company: "Google",
      image: "/assets/img/hero.jpg",
      testimonial: "The AI mock interviews helped me identify my weak points and improve my confidence. I landed my dream job at Google!",
      rating: 5,
      verified: true
    },
    {
      name: "Michael Chen",
      role: "Product Manager",
      company: "Microsoft",
      image: "/assets/img/office.jpg",
      testimonial: "The personalized feedback was incredibly valuable. I felt much more prepared for my interviews.",
      rating: 5,
      verified: true
    },
    {
      name: "Emily Rodriguez",
      role: "Data Scientist",
      company: "Amazon",
      image: "/assets/img/hero.jpg",
      testimonial: "This platform completely transformed my interview approach. The AI feedback was spot-on and helped me improve dramatically.",
      rating: 5,
      verified: true
    }
  ];

  const services = [
    {
      title: "AI Mock Interviews",
      description: "Practice with our advanced AI interviewer that provides real-time feedback and personalized insights.",
      features: ["Real-time feedback", "Industry-specific questions", "Performance analytics", "Unlimited practice"],
      price: "Free",
      rating: 4.9,
      image: "/assets/img/hero.jpg",
      href: "/generate",
      popular: true
    },
    {
      title: "Career Coaching",
      description: "Get personalized guidance from industry experts to accelerate your professional growth.",
      features: ["1-on-1 sessions", "Career roadmap", "Industry insights", "Goal tracking"],
      price: "$99/month",
      rating: 4.8,
      image: "/assets/img/office.jpg",
      href: "/services"
    },
    {
      title: "Resume Building",
      description: "Create ATS-optimized resumes that get noticed by recruiters and hiring managers.",
      features: ["ATS-optimized", "Professional review", "Multiple formats", "Cover letter"],
      price: "$49",
      rating: 4.7,
      image: "/assets/img/office.jpg",
      href: "/services"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <Container>
        <HeroSection
          badge="🚀 AI-Powered Interview Preparation"
          title="Master Your Next Interview"
          subtitle="With AI Superpower"
          description="Boost your interview skills and increase your success rate with AI-driven insights. Discover a smarter way to prepare, practice, and stand out from the competition."
          primaryCta={{
            text: "Start Free Trial",
            href: "/generate"
          }}
          secondaryCta={{
            text: "Watch Demo",
            href: "#demo"
          }}
          image="/assets/img/hero.jpg"
        />
      </Container>

      {/* Company Logos Marquee */}
      <div className="w-full my-16">
        <Container>
          <p className="text-center text-muted-foreground mb-8">Trusted by professionals at top companies</p>
        </Container>
        <Marquee pauseOnHover>
          <MarqueImg img="/assets/img/logo/firebase.png" alt="Firebase" />
          <MarqueImg img="/assets/img/logo/meet.png" alt="Google Meet" />
          <MarqueImg img="/assets/img/logo/zoom.png" alt="Zoom" />
          <MarqueImg img="/assets/img/logo/microsoft.png" alt="Microsoft" />
          <MarqueImg img="/assets/img/logo/react.png" alt="React" />
          <MarqueImg img="/assets/img/logo/tailwindcss.png" alt="Tailwind CSS" />
          <MarqueImg img="/assets/img/logo/firebase.png" alt="Firebase" />
          <MarqueImg img="/assets/img/logo/meet.png" alt="Google Meet" />
        </Marquee>
      </div>

      {/* Stats Section */}
      <Container>
        <StatsSection
          title="Trusted by Professionals Worldwide"
          subtitle="Join thousands of successful candidates who have transformed their careers with our AI-powered platform."
          stats={stats}
        />
      </Container>

      {/* Features Section */}
      <div className="bg-muted/50">
        <Container>
          <FeatureSection
            title="Why Choose Our Platform?"
            subtitle="We combine cutting-edge AI technology with proven interview strategies to help you succeed."
            features={features}
          />
        </Container>
      </div>

      {/* Services Preview */}
      <Container className="py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Services</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Comprehensive career development solutions designed to help you land your dream job.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {services.map((service, index) => (
            <ServiceCard key={index} {...service} />
          ))}
        </div>
        
        <div className="text-center">
          <Link to="/services">
            <Button size="lg" variant="outline">
              View All Services
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </Link>
        </div>
      </Container>

      {/* Testimonials */}
      <div className="bg-muted/50">
        <Container className="py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Success Stories</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Hear from professionals who have transformed their careers with our platform.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard key={index} {...testimonial} />
            ))}
          </div>
        </Container>
      </div>

      {/* CTA Section */}
      <Container className="py-16">
        <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20">
          <CardContent className="p-12 text-center">
            <Badge variant="outline" className="mb-4">
              Ready to Get Started?
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Transform Your Interview Skills Today
            </h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join thousands of professionals who have already improved their interview performance and landed their dream jobs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/generate">
                <Button size="lg">
                  Start Free Trial
                  <Sparkles className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link to="/contact">
                <Button size="lg" variant="outline">
                  Contact Sales
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </Container>
    </div>
  );
};

export default HomePage;
