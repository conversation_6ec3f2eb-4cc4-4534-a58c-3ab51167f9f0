import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Linkedin, Twitter, Mail } from "lucide-react";

interface TeamCardProps {
  name: string;
  role: string;
  bio: string;
  image: string;
  skills: string[];
  social?: {
    linkedin?: string;
    twitter?: string;
    email?: string;
  };
}

export const TeamCard = ({
  name,
  role,
  bio,
  image,
  skills,
  social,
}: TeamCardProps) => {
  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg group">
      <div className="relative h-64 overflow-hidden">
        <img
          src={image}
          alt={name}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
        
        {/* Social links overlay */}
        <div className="absolute bottom-4 left-4 right-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          {social?.linkedin && (
            <Button size="sm" variant="secondary" className="p-2" asChild>
              <a href={social.linkedin} target="_blank" rel="noopener noreferrer">
                <Linkedin className="w-4 h-4" />
              </a>
            </Button>
          )}
          {social?.twitter && (
            <Button size="sm" variant="secondary" className="p-2" asChild>
              <a href={social.twitter} target="_blank" rel="noopener noreferrer">
                <Twitter className="w-4 h-4" />
              </a>
            </Button>
          )}
          {social?.email && (
            <Button size="sm" variant="secondary" className="p-2" asChild>
              <a href={`mailto:${social.email}`}>
                <Mail className="w-4 h-4" />
              </a>
            </Button>
          )}
        </div>
      </div>
      
      <CardContent className="p-6">
        <h3 className="text-xl font-semibold mb-1">{name}</h3>
        <p className="text-primary font-medium mb-3">{role}</p>
        <p className="text-sm text-muted-foreground mb-4 leading-relaxed">
          {bio}
        </p>
        
        <div className="flex flex-wrap gap-2">
          {skills.map((skill, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {skill}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
