{"name": "ai-mock-interview-react-type", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.22.6", "@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.2.0", "lucide-react": "^0.474.0", "next-themes": "^0.4.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.54.2", "react-hook-speech-to-text": "^0.8.0", "react-router-dom": "^7.1.3", "react-webcam": "^7.2.0", "sonner": "^1.7.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^22.10.10", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}