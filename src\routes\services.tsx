import { Container } from "@/components/container";
import { HeroSection } from "@/components/hero-section";
import { ServiceCard } from "@/components/service-card";
import { FeatureSection } from "@/components/feature-section";
import { StatsSection } from "@/components/stats-section";
import { TestimonialCard } from "@/components/testimonial-card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Brain, 
  Target, 
  Users, 
  Award, 
  Clock, 
  CheckCircle,
  MessageSquare,
  FileText,
  Video
} from "lucide-react";
import { Link } from "react-router-dom";

const ServicesPage = () => {
  const services = [
    {
      title: "AI Mock Interviews",
      description: "Practice with our advanced AI interviewer that adapts to your responses and provides real-time feedback.",
      features: [
        "Real-time AI feedback",
        "Industry-specific questions",
        "Performance analytics",
        "Unlimited practice sessions"
      ],
      price: "Free",
      rating: 4.9,
      image: "/assets/img/hero.jpg",
      href: "/generate",
      popular: true
    },
    {
      title: "Career Coaching",
      description: "Get personalized career guidance from industry experts to accelerate your professional growth.",
      features: [
        "1-on-1 coaching sessions",
        "Career roadmap planning",
        "Industry insights",
        "Goal setting & tracking"
      ],
      price: "$99/month",
      rating: 4.8,
      image: "/assets/img/office.jpg",
      href: "/contact"
    },
    {
      title: "Resume Building",
      description: "Create ATS-optimized resumes that get noticed by recruiters and hiring managers.",
      features: [
        "ATS-optimized templates",
        "Professional review",
        "Industry-specific formats",
        "Cover letter included"
      ],
      price: "$49",
      rating: 4.7,
      image: "/assets/img/office.jpg",
      href: "/contact"
    }
  ];

  const features = [
    {
      icon: Brain,
      title: "AI-Powered Intelligence",
      description: "Our advanced AI analyzes your responses and provides personalized feedback to improve your interview performance."
    },
    {
      icon: Target,
      title: "Industry-Specific Training",
      description: "Practice with questions tailored to your specific industry and role, ensuring relevant preparation."
    },
    {
      icon: Users,
      title: "Expert Guidance",
      description: "Learn from industry professionals who understand what employers are looking for in candidates."
    },
    {
      icon: Award,
      title: "Proven Results",
      description: "Join thousands of successful candidates who have landed their dream jobs using our platform."
    },
    {
      icon: Clock,
      title: "24/7 Availability",
      description: "Practice anytime, anywhere with our always-available AI interviewer and resources."
    },
    {
      icon: CheckCircle,
      title: "Progress Tracking",
      description: "Monitor your improvement with detailed analytics and performance metrics over time."
    }
  ];

  const stats = [
    { value: "50K+", label: "Successful Interviews", description: "Candidates placed in top companies" },
    { value: "95%", label: "Success Rate", description: "Of users get job offers" },
    { value: "500+", label: "Companies", description: "Trust our training methods" },
    { value: "24/7", label: "Support", description: "Always here to help you" }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Software Engineer",
      company: "Google",
      image: "/assets/img/hero.jpg",
      testimonial: "The AI mock interviews helped me identify my weak points and improve my confidence. I landed my dream job at Google!",
      rating: 5,
      verified: true
    },
    {
      name: "Michael Chen",
      role: "Product Manager",
      company: "Microsoft",
      image: "/assets/img/office.jpg",
      testimonial: "The personalized feedback was incredibly valuable. I felt much more prepared for my interviews.",
      rating: 5,
      verified: true
    }
  ];

  return (
    <div className="min-h-screen">
      <Container>
        <HeroSection
          badge="🚀 Transform Your Career"
          title="Professional Services"
          subtitle="Designed for Success"
          description="Comprehensive career development services powered by AI and backed by industry experts. Get the competitive edge you need to land your dream job."
          primaryCta={{
            text: "Start Free Trial",
            href: "/generate"
          }}
          secondaryCta={{
            text: "Watch Demo",
            href: "#demo"
          }}
          image="/assets/img/hero.jpg"
        />
      </Container>

      {/* Services Grid */}
      <Container className="py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Services</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Choose from our comprehensive suite of career development services designed to help you succeed.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <ServiceCard key={index} {...service} />
          ))}
        </div>
      </Container>

      {/* Features Section */}
      <div className="bg-muted/50">
        <Container>
          <FeatureSection
            title="Why Choose Our Services?"
            subtitle="We combine cutting-edge AI technology with human expertise to deliver unparalleled career development solutions."
            features={features}
          />
        </Container>
      </div>

      {/* Stats Section */}
      <Container>
        <StatsSection
          title="Trusted by Professionals Worldwide"
          subtitle="Join thousands of successful candidates who have transformed their careers with our services."
          stats={stats}
        />
      </Container>

      {/* Testimonials */}
      <div className="bg-muted/50">
        <Container className="py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Success Stories</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Hear from professionals who have transformed their careers with our services.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard key={index} {...testimonial} />
            ))}
          </div>
        </Container>
      </div>

      {/* CTA Section */}
      <Container className="py-16">
        <div className="text-center space-y-6">
          <h2 className="text-3xl md:text-4xl font-bold">Ready to Transform Your Career?</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Start your journey today with our AI-powered interview preparation platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/generate">
              <Button size="lg">
                Start Free Trial
              </Button>
            </Link>
            <Link to="/contact">
              <Button size="lg" variant="outline">
                Contact Sales
              </Button>
            </Link>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default ServicesPage;
