import { Card, CardContent } from "@/components/ui/card";

interface StatProps {
  value: string;
  label: string;
  description?: string;
}

interface StatsSectionProps {
  title?: string;
  subtitle?: string;
  stats: StatProps[];
  className?: string;
}

export const StatsSection = ({
  title,
  subtitle,
  stats,
  className = "",
}: StatsSectionProps) => {
  return (
    <section className={`py-16 ${className}`}>
      {(title || subtitle) && (
        <div className="text-center mb-12">
          {title && (
            <h2 className="text-3xl md:text-4xl font-bold mb-4">{title}</h2>
          )}
          {subtitle && (
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {subtitle}
            </p>
          )}
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>
    </section>
  );
};

const StatCard = ({ value, label, description }: StatProps) => {
  return (
    <Card className="text-center transition-all duration-300 hover:shadow-lg">
      <CardContent className="p-6">
        <div className="text-4xl md:text-5xl font-bold text-primary mb-2">
          {value}
        </div>
        <h3 className="text-lg font-semibold mb-2">{label}</h3>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </CardContent>
    </Card>
  );
};
