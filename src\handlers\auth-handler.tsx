import { db } from "@/config/firebase.config";
import { LoaderPage } from "@/routes/loader-page";
import { User } from "@/types";
import { useAuth, useUser } from "@clerk/clerk-react";
import { doc, getDoc, serverTimestamp, setDoc } from "firebase/firestore";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

const AuthHanlder = () => {
  const { isSignedIn } = useAuth();
  const { user } = useUser();

  const pathname = useLocation().pathname;
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const storeUserData = async () => {
      if (isSignedIn && user) {
        setLoading(true);
        try {
          // Check if user document exists
          const userSnap = await getDoc(doc(db, "users", user.id));

          if (!userSnap.exists()) {
            const userData: User = {
              id: user.id,
              name: user.fullName || user.firstName || "Anonymous",
              email: user.primaryEmailAddress?.emailAddress || "N/A",
              imageUrl: user.imageUrl,
              createdAt: serverTimestamp(),
              updateAt: serverTimestamp(),
            };

            await setDoc(doc(db, "users", user.id), userData);
            console.log("User data stored successfully");
          } else {
            console.log("User already exists in database");
          }
        } catch (error) {
          console.error("Error storing user data:", error);

          // More specific error handling
          if (error instanceof Error) {
            if (error.message.includes("permission-denied")) {
              console.error("Permission denied: Check Firestore security rules");
            } else if (error.message.includes("unavailable")) {
              console.error("Firestore service unavailable");
            } else if (error.message.includes("unauthenticated")) {
              console.error("User not authenticated properly");
            }
          }

          // Don't throw the error to prevent app crash
          // Just log it and continue
        } finally {
          setLoading(false);
        }
      }
    };

    // Only run if user is signed in
    if (isSignedIn && user) {
      storeUserData();
    }
  }, [isSignedIn, user]);

  if (loading) {
    return <LoaderPage />;
  }

  return null;
};

export default AuthHanlder;
