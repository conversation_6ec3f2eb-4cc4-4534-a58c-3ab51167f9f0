<svg width="492" height="341" viewBox="0 0 492 341" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M399.532 270.861C277.951 257.395 141.133 261.885 60.8777 279.332C32.8724 285.42 33.7377 314.848 61.4738 322.064C165.231 349.058 305.276 344.816 405.106 319.498C438.183 311.109 433.449 274.617 399.532 270.861Z" fill="#00D296" fill-opacity="0.1"/>
<g filter="url(#filter0_d_33_14)">
<rect x="73.5" y="78.5" width="331" height="215" rx="16.5" fill="white" stroke="#00D296" stroke-width="5"/>
<path d="M74 217.5C57.3292 218.902 49.0573 219.091 45.4779 218.035C29.3215 213.271 39.0653 190.791 22.406 188.302L7 186" stroke="#00D296" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M404 212.5C420.671 213.902 428.943 214.091 432.522 213.035C448.679 208.271 438.935 185.791 455.594 183.302L471 181" stroke="#00D296" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
<line x1="73" y1="123.5" x2="404" y2="123.5" stroke="#00D296" stroke-width="5"/>
<circle cx="100.5" cy="102.5" r="4.5" fill="#00D296"/>
<circle cx="113.5" cy="102.5" r="4.5" fill="#00D296"/>
<circle cx="126.5" cy="102.5" r="4.5" fill="#00D296"/>
<circle cx="198" cy="176" r="5" fill="#00D296"/>
<circle cx="283" cy="176" r="5" fill="#00D296"/>
<circle cx="139.5" cy="102.5" r="4.5" fill="#00D296"/>
<path d="M233 210.056C238.241 209.65 249.379 211.272 252 221" stroke="#00D296" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<g clip-path="url(#clip0_33_14)">
<path d="M368.209 70.3117C370.527 68.6227 373.528 68.1914 376.223 69.1617C380.985 70.8867 386.322 71.875 392 71.875C414.407 71.875 429.375 57.4102 429.375 43.125C429.375 28.8398 414.407 14.375 392 14.375C369.593 14.375 354.625 28.8398 354.625 43.125C354.625 48.875 356.853 54.4094 361.04 59.1531C362.585 60.8961 363.34 63.1961 363.16 65.532C362.908 68.7844 362.136 71.7672 361.13 74.4086C364.184 72.9891 366.718 71.4078 368.209 70.3297V70.3117ZM349.809 77.607C350.133 77.1219 350.438 76.6367 350.726 76.1516C352.523 73.1687 354.23 69.2516 354.571 64.8492C349.18 58.7219 346 51.2289 346 43.125C346 22.4789 366.592 5.75 392 5.75C417.408 5.75 438 22.4789 438 43.125C438 63.7711 417.408 80.5 392 80.5C385.333 80.5 379.008 79.35 373.294 77.2836C371.156 78.8469 367.67 80.9852 363.537 82.782C360.824 83.968 357.733 85.0461 354.535 85.675C354.391 85.7109 354.248 85.7289 354.104 85.7648C353.313 85.9086 352.54 86.0344 351.732 86.1063C351.696 86.1063 351.642 86.1242 351.606 86.1242C350.69 86.2141 349.773 86.268 348.857 86.268C347.689 86.268 346.647 85.5672 346.198 84.4891C345.748 83.4109 346 82.1891 346.808 81.3625C347.545 80.6078 348.21 79.7992 348.839 78.9367C349.144 78.5234 349.432 78.1102 349.701 77.6969L349.755 77.607H349.809Z" fill="#00D296"/>
<path d="M385.25 34.5C385.25 31.7422 387.492 29.5 390.25 29.5H392.75C395.508 29.5 397.75 31.7422 397.75 34.5V34.7813C397.75 36.4844 396.883 38.0703 395.453 38.9844L392.156 41.1016C390.187 42.3672 389 44.5469 389 46.8828V46.9922C389 48.375 390.117 49.4922 391.5 49.4922C392.883 49.4922 394 48.375 394 46.9922V46.8828C394 46.2422 394.328 45.6484 394.859 45.3047L398.156 43.1875C401.016 41.3438 402.75 38.1797 402.75 34.7734V34.4922C402.75 28.9687 398.273 24.4922 392.75 24.4922H390.25C384.727 24.5 380.25 28.9766 380.25 34.5C380.25 35.8828 381.367 37 382.75 37C384.133 37 385.25 35.8828 385.25 34.5ZM391.5 59.5C392.329 59.5 393.124 59.1708 393.71 58.5847C394.296 57.9987 394.625 57.2038 394.625 56.375C394.625 55.5462 394.296 54.7513 393.71 54.1653C393.124 53.5792 392.329 53.25 391.5 53.25C390.671 53.25 389.876 53.5792 389.29 54.1653C388.704 54.7513 388.375 55.5462 388.375 56.375C388.375 57.2038 388.704 57.9987 389.29 58.5847C389.876 59.1708 390.671 59.5 391.5 59.5Z" fill="#00D296"/>
</g>
<defs>
<filter id="filter0_d_33_14" x="0.5" y="76" width="477" height="228" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_33_14"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_33_14" result="shape"/>
</filter>
<clipPath id="clip0_33_14">
<rect width="92" height="92" fill="white" transform="translate(346)"/>
</clipPath>
</defs>
</svg>
