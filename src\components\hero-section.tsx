import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Play } from "lucide-react";
import { Link } from "react-router-dom";

interface HeroSectionProps {
  badge?: string;
  title: string;
  subtitle: string;
  description: string;
  primaryCta: {
    text: string;
    href: string;
  };
  secondaryCta?: {
    text: string;
    href: string;
  };
  image?: string;
  video?: string;
  className?: string;
}

export const HeroSection = ({
  badge,
  title,
  subtitle,
  description,
  primaryCta,
  secondaryCta,
  image,
  video,
  className = "",
}: HeroSectionProps) => {
  return (
    <section className={`py-20 md:py-32 ${className}`}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        {/* Content */}
        <div className="space-y-8">
          {badge && (
            <Badge variant="outline" className="w-fit">
              {badge}
            </Badge>
          )}
          
          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl font-bold leading-tight">
              {title}
            </h1>
            <h2 className="text-2xl md:text-3xl text-muted-foreground font-medium">
              {subtitle}
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed max-w-lg">
              {description}
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <Link to={primaryCta.href}>
              <Button size="lg" className="group">
                {primaryCta.text}
                <ArrowRight className="w-5 h-5 ml-2 transition-transform group-hover:translate-x-1" />
              </Button>
            </Link>
            
            {secondaryCta && (
              <Link to={secondaryCta.href}>
                <Button size="lg" variant="outline" className="group">
                  <Play className="w-5 h-5 mr-2" />
                  {secondaryCta.text}
                </Button>
              </Link>
            )}
          </div>
        </div>
        
        {/* Media */}
        <div className="relative">
          {image && (
            <div className="relative rounded-2xl overflow-hidden shadow-2xl">
              <img
                src={image}
                alt="Hero"
                className="w-full h-auto object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            </div>
          )}
          
          {video && (
            <div className="relative rounded-2xl overflow-hidden shadow-2xl">
              <video
                src={video}
                autoPlay
                muted
                loop
                className="w-full h-auto object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            </div>
          )}
          
          {/* Decorative elements */}
          <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl" />
          <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-secondary/20 rounded-full blur-xl" />
        </div>
      </div>
    </section>
  );
};
